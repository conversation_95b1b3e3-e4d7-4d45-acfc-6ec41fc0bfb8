﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel.Security;
using System.Threading;
using DevExpress.XtraEditors;
using eBay.Services;
using eBay.Services.Finding;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.Search.FIA
{
    internal class FiaService
    {
        public static int FindItemsAdvancedFails;
        private bool _callsExceededMessageDisabled;
        private readonly ViewReporter _viewReporter;

        public FiaService(ViewReporter viewReporter)
        {
            _viewReporter = viewReporter;
        }

        public void FindRequestsApi(CancellationToken ct, Keyword2Find kw2Find, bool isEndingNow, bool isInitialSearch, FindingAPISortOrder sortOrder = FindingAPISortOrder.Default)
        {
            if (ct.IsCancellationRequested || !Form1.Instance.IsHandleCreated)
                return;

            Interlocked.Increment(ref Stat.FindReqCount);
            Interlocked.Increment(ref Stat.FindApiCounter);
            Interlocked.Increment(ref Stat.FindActiveThreads);

            var findingServiceClient = CreateFindingServiceClient(kw2Find.EBaySite.GlobalID);

            FindItemsAdvancedRequest findItemsAdvancedRequest;
            if (isEndingNow)
            {
                // Use custom sort order if specified, otherwise use the pre-built request
                if (sortOrder != FindingAPISortOrder.Default)
                {
                    findItemsAdvancedRequest = FiaBuilder.CreateEndingNowRequest(kw2Find, sortOrder);
                }
                else
                {
                    findItemsAdvancedRequest = kw2Find.RequestEndNow;
                }

                if (findItemsAdvancedRequest.itemFilter.Length == 0)
                    return;

                var itemFilter = findItemsAdvancedRequest.itemFilter.FirstOrDefault(f => f.name == ItemFilterType.EndTimeTo);

                if (itemFilter != null)
                    itemFilter.value = new[] { DateTime.UtcNow.AddMinutes(15).ToString(@"yyyy-MM-dd\THH:mm:ss.fff\Z") };
                else
                    return;
            }
            else
            {
                // Use custom sort order if specified, otherwise use the pre-built request
                if (sortOrder != FindingAPISortOrder.Default)
                {
                    findItemsAdvancedRequest = FiaBuilder.CreateBINAuctionRequest(kw2Find, sortOrder);
                }
                else
                {
                    findItemsAdvancedRequest = kw2Find.BinAuctionRequest;
                }
            }

            FindItemsAdvancedResponse response = null;
            try
            {
                response = findingServiceClient.findItemsAdvanced(findItemsAdvancedRequest);
            }
            catch (SecurityNegotiationException ex)
            {
                if (!Loggers.BadCertificateSent)
                {
                    Loggers.LogCertError($"{Loggers.BadCertificate};{ex.Message}");
                    Loggers.BadCertificateSent = true;
                    XtraMessageBox.Show("Certificate error #443.\r\nPlease, contact uBuyFirst support.");
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("This is often caused by an incorrect address or SOAP action"))
                    return;

                if (ex.Message.Contains("Service call has exceeded the number of times"))
                {
                    if (FindItemsAdvancedFails <= 0)
                    {
                        FindItemsAdvancedFails += 500;
                        if (!_callsExceededMessageDisabled)
                        {
                            ExM.ubuyExceptionHandler("_(info)_findItemsAdvanced: ", ex);
                            _callsExceededMessageDisabled = true;
                        }
                    }

                    return;
                }

                ExM.ubuyExceptionHandler("_(info)_findItemsAdvanced: ", ex);
            }
            finally
            {
                Interlocked.Decrement(ref Stat.FindActiveThreads);
            }

            if (response?.searchResult != null && response.ack == AckValue.Success)
            {
                if (response.searchResult.item == null)
                    return;

                if (ct.IsCancellationRequested)
                    return;

                var fiaItems = response.searchResult.item.ToList();
                var itemIDs = fiaItems.Select(item => item.itemId).ToList();

                List<string> addedItems;
                if (kw2Find.ChildrenCore.Count == 0)
                {
                    addedItems = SearchService.AddItemsToStorage(itemIDs);
                }
                else
                {
                    addedItems = SearchService.AddItemsToStorage(kw2Find, itemIDs);
                }

                var newFoundItems = new List<FoundItem>();
                foreach (var addedItem in addedItems)
                {
                    var searchItem = fiaItems.FirstOrDefault(fiaItem => addedItem.Contains(fiaItem.itemId));
                    if (searchItem != null)
                    {
                        var isMultiVariation = searchItem.isMultiVariationListing;
                        var foundItem = new FoundItem(null, kw2Find, searchItem.itemId, searchItem.listingInfo.startTime, SearchSource.API, isMultiVariation, isInitialSearch, searchItem.sellerInfo.sellerUserName, null, null,new string[]{ searchItem.primaryCategory.categoryId });
                        newFoundItems.Add(foundItem);
                    }
                }

                SearchService.SendItemsForProcessing(newFoundItems, _viewReporter);
                Status.RowStatusUpdater.SetStatusActive(itemIDs.Except(addedItems).ToList());
            }
            else
            {
                if (response?.ack == AckValue.Failure)
                {
                    if (response.errorMessage?.Any() != true || ct.IsCancellationRequested)
                        return;

                    if (response.errorMessage[0].message == "Invalid postal code for specified country.")
                    {
                        if (ProgramState.Isdebug)
                        {
                            Stat._errorsCount++;
                            _viewReporter.ReportLogTxt(DateTime.Now.ToString("d MMM HH:mm:ss") + " \t " + findItemsAdvancedRequest.keywords + " \t " + response.errorMessage[0].message);
                            kw2Find.IgnorePostalCodeError = true;
                            Form1.Instance.notifyIcon1.BalloonTipTitle = "Wrong postal code.";
                            Form1.Instance.notifyIcon1.BalloonTipText = $"'{kw2Find.Alias}' seems to have wrong postal code for specified country.";
                            Form1.Instance.notifyIcon1.ShowBalloonTip(60000);
                        }
                    }
                    else
                    {
                        Stat._errorsCount++;
                        _viewReporter.ReportLogTxt(DateTime.Now.ToString("d MMM HH:mm:ss") + " \t " + findItemsAdvancedRequest.keywords + " \t " + response.errorMessage[0].message);
                    }
                }
            }
        }

        public FindingServicePortTypeClient CreateFindingServiceClient(string globalID)
        {
            var clientConfig = new ClientConfig();
            clientConfig.ApplicationId = ConnectionConfig.FindingApiKey;
            clientConfig.EndPointAddress = $"{ConnectionConfig.FindingApiHost}/services/search/FindingService/v1";
            clientConfig.GlobalId = globalID;
            var client = FindingServiceClientFactory.getServiceClient(clientConfig);

            return client;
        }
    }
}
