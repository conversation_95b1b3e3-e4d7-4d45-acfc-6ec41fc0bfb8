﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using uBuyFirst.BrowseAPI;
using uBuyFirst.Prefs;
using uBuyFirst.Search.FIA;

namespace uBuyFirst
{
    internal class EndNowFinder
    {
        public bool IsEndNowRunning { get; private set; }
        private const int EndNowInterval = 30;
        private readonly FiaService _fiaService;
        private readonly BrowseAPINetwork _browseAPINetwork;
        const int MaxKeywordsForInitialSearch = 30;

        public EndNowFinder(FiaService fiaService, BrowseAPINetwork browseAPINetwork)
        {
            _browseAPINetwork = browseAPINetwork;
            _fiaService = fiaService;
        }

        public void PerformInitialSearch(List<Keyword2Find> enabledKeywords, CancellationToken tokenCancelSearch, FindingAPISortOrder sortOrder = FindingAPISortOrder.Default)
        {
            var eligibleKeywords = enabledKeywords.Where(kw => kw.IsEndingNow).ToList();
            if (eligibleKeywords.Count == 0)
                return;

            IsEndNowRunning = true;
            for (var i = 0; i < eligibleKeywords.Count; i++)
            {
                if (tokenCancelSearch.IsCancellationRequested)
                {
                    IsEndNowRunning = false;
                    return;
                }

                var kw = eligibleKeywords[i];
                _fiaService.FindRequestsApi(tokenCancelSearch, kw, true, false, sortOrder);

                if (i > MaxKeywordsForInitialSearch)
                    break;
            }
        }

        public void StartSearchLoop(CancellationToken tokenCancelSearch, List<Keyword2Find> enabledKeywords)
        {
            var eligibleKeywords = enabledKeywords.Where(kw => kw.IsEndingNow).ToList();
            if (eligibleKeywords.Count == 0)
                return;

            Task.Run(async () =>
            {
                IsEndNowRunning = true;
                eligibleKeywords.ForEach(kw => kw.RequestEndNow.paginationInput.entriesPerPage = 100);

                while (!tokenCancelSearch.IsCancellationRequested)
                {
                    if (!ConnectionConfig.FindingAPIEnabled)
                        return;

                    await Task.Delay(5000, tokenCancelSearch).ContinueWith(t => { });
                    foreach (var kw in eligibleKeywords)
                    {
                        var lastChecked = (DateTime.UtcNow - kw.EndNowRequestTimestamp).TotalSeconds;
                        if (!(lastChecked > EndNowInterval))
                            continue;

                        kw.EndNowRequestTimestamp = DateTime.UtcNow;

                        _fiaService.FindRequestsApi(tokenCancelSearch, kw, true, false);

                        await Task.Delay(5000, tokenCancelSearch).ContinueWith(t => { });
                    }
                }
            }, tokenCancelSearch).ContinueWith(t => { return IsEndNowRunning = false; });
        }

        public void PerformInitialSearchBrowseAPI(List<Keyword2Find> enabledKeywords, CancellationToken tokenCancelSearch, BrowseAPISortOrder sortOrder = BrowseAPISortOrder.Default)
        {
            var eligibleKeywords = enabledKeywords.Where(kw => kw.IsEndingNow).ToList();
            if (eligibleKeywords.Count == 0)
                return;

            IsEndNowRunning = true;
            for (var i = 0; i < eligibleKeywords.Count; i++)
            {
                if (tokenCancelSearch.IsCancellationRequested)
                {
                    IsEndNowRunning = false;
                    return;
                }

                var kw = eligibleKeywords[i];
                foreach (var categoryID in kw.Categories4Api.Split(','))
                    _browseAPINetwork.FindRequestsApi(tokenCancelSearch, kw, categoryID, true, true, sortOrder);

                if (i > MaxKeywordsForInitialSearch)
                    break;
            }
        }

        public void StartSearchLoopBrowseAPI(CancellationToken tokenCancelSearch, List<Keyword2Find> enabledKeywords)
        {
            var eligibleKeywords = enabledKeywords.Where(kw => kw.IsEndingNow).ToList();
            if (eligibleKeywords.Count == 0)
                return;

            Task.Run(async () =>
            {
                IsEndNowRunning = true;
                eligibleKeywords.ForEach(kw => kw.RequestEndNow.paginationInput.entriesPerPage = 100);

                while (!tokenCancelSearch.IsCancellationRequested)
                {
                    await Task.Delay(5000, tokenCancelSearch).ContinueWith(t => { });
                    foreach (var kw in eligibleKeywords)
                    {
                        var lastChecked = (DateTime.UtcNow - kw.EndNowRequestTimestamp).TotalSeconds;
                        if (!(lastChecked > EndNowInterval))
                            continue;

                        kw.EndNowRequestTimestamp = DateTime.UtcNow;
                        foreach (var categoryID in kw.Categories4Api.Split(','))
                            _browseAPINetwork.FindRequestsApi(tokenCancelSearch, kw, categoryID, true, false);

                        await Task.Delay(5000, tokenCancelSearch).ContinueWith(t => { });
                    }
                }
            }, tokenCancelSearch).ContinueWith(t => { return IsEndNowRunning = false; });
        }
    }
}
